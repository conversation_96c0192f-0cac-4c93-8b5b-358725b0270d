import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'dart:io';

/// خدمة الحصول على معلومات الجهاز
class DeviceInfoService {
  static DeviceInfoService? _instance;
  static DeviceInfoService get instance => _instance ??= DeviceInfoService._();
  
  DeviceInfoService._();
  
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  /// معلومات الجهاز المخزنة محلياً
  static Map<String, dynamic>? _deviceData;

  /// Platform channel للحصول على معلومات إضافية للجهاز
  static const MethodChannel _platformChannel = MethodChannel('device_serial_channel');
  
  /// الحصول على معلومات الجهاز المحفوظة
  static Map<String, dynamic>? get deviceData => _deviceData;
  
  /// الحصول على سيريال الجهاز المحسن
  static String? get deviceSerial {
    if (_deviceData == null) return null;

    // ترتيب الأولوية حسب المنصة مع تحسينات
    if (_deviceData!['platform'] == 'Android') {
      // للأندرويد: ترتيب أولوية محسن
      return _getAndroidSerial();
    } else if (_deviceData!['platform'] == 'iOS') {
      // لـ iOS: استخدم identifierForVendor
      return _deviceData!['identifierForVendor'] ?? _deviceData!['serial'];
    } else if (_deviceData!['platform'] == 'Windows') {
      // لـ Windows: استخدم السيريال الحقيقي أولاً
      return _getWindowsSerial();
    } else if (_deviceData!['platform'] == 'macOS') {
      // لـ macOS: استخدم systemGUID
      return _deviceData!['systemGUID'] ?? _deviceData!['serial'];
    } else if (_deviceData!['platform'] == 'Linux') {
      // لـ Linux: استخدم machineId
      return _deviceData!['machineId'] ?? _deviceData!['serial'];
    }

    // احتياطي عام
    final fallbackSerial = _deviceData!['serial'] ?? _deviceData!['serialNumber'] ?? _deviceData!['androidId'] ?? _deviceData!['deviceId'];

    // إذا لم نجد أي معرف، أنشئ معرف فريد من معلومات الجهاز
    if (fallbackSerial == null || fallbackSerial.toString().isEmpty) {
      return _generateFallbackSerial();
    }

    return fallbackSerial;
  }

  /// الحصول على سيريال الأندرويد بطريقة محسنة
  static String? _getAndroidSerial() {
    // ترتيب الأولوية للأندرويد:
    // 1. السيريال الفعلي إذا كان متوفراً وليس "unknown"
    // 2. Android ID (الأكثر استقراراً)
    // 3. Build fingerprint (فريد لكل ROM)
    // 4. معرف مركب من معلومات الجهاز

    String? serial = _deviceData!['serial'];
    if (serial != null && serial.isNotEmpty && serial.toLowerCase() != 'unknown') {
      return serial;
    }

    String? androidId = _deviceData!['androidId'];
    if (androidId != null && androidId.isNotEmpty) {
      return androidId;
    }

    String? fingerprint = _deviceData!['fingerprint'];
    if (fingerprint != null && fingerprint.isNotEmpty) {
      return fingerprint;
    }

    // إنشاء معرف مركب من معلومات الجهاز
    return _generateAndroidFallbackSerial();
  }

  /// الحصول على سيريال الويندوز بطريقة محسنة
  static String? _getWindowsSerial() {
    // ترتيب الأولوية للويندوز مع التحسينات:
    // 1. Enhanced Serial (من Platform Channel)
    // 2. Device ID (معرف الجهاز الفريد)
    // 3. Product ID (معرف المنتج)
    // 4. Digital Product ID (معرف رقمي)
    // 5. Build Lab (معلومات البناء)
    // 6. Computer Name (كحل أخير)

    // أولاً، تحقق من السيريال المحسن
    String? enhancedSerial = _deviceData!['enhancedSerial'];
    if (enhancedSerial != null && enhancedSerial.isNotEmpty) {
      return enhancedSerial;
    }

    String? deviceId = _deviceData!['deviceId'];
    if (deviceId != null && deviceId.isNotEmpty && deviceId != 'null') {
      return deviceId;
    }

    String? productId = _deviceData!['productId'];
    if (productId != null && productId.isNotEmpty && productId != 'null') {
      return productId;
    }

    String? digitalProductId = _deviceData!['digitalProductId'];
    if (digitalProductId != null && digitalProductId.isNotEmpty && digitalProductId != 'null') {
      return digitalProductId;
    }

    String? buildLab = _deviceData!['buildLab'];
    if (buildLab != null && buildLab.isNotEmpty && buildLab != 'null') {
      return buildLab;
    }

    // كحل أخير، استخدم اسم الكمبيوتر
    String? computerName = _deviceData!['computerName'];
    if (computerName != null && computerName.isNotEmpty && computerName != 'null') {
      return computerName;
    }

    return null;
  }

  /// إنشاء معرف احتياطي للأندرويد
  static String _generateAndroidFallbackSerial() {
    final model = _deviceData!['model'] ?? 'Unknown';
    final manufacturer = _deviceData!['manufacturer'] ?? 'Unknown';
    final brand = _deviceData!['brand'] ?? 'Unknown';
    final device = _deviceData!['device'] ?? 'Unknown';
    final board = _deviceData!['board'] ?? 'Unknown';
    final hardware = _deviceData!['hardware'] ?? 'Unknown';

    // إنشاء معرف مركب من معلومات الجهاز
    final combinedInfo = '$manufacturer-$brand-$model-$device-$board-$hardware';

    // إنشاء hash من المعلومات المجمعة
    int hash = combinedInfo.hashCode;
    if (hash < 0) hash = -hash;

    return 'ANDROID_${hash.toString().padLeft(10, '0')}';
  }

  /// الحصول على سيريال الأندرويد المحسن باستخدام طرق إضافية
  static Future<String?> _getEnhancedAndroidSerial() async {
    try {
      // محاولة استخدام platform channel للحصول على معلومات إضافية
      final result = await _platformChannel.invokeMethod('getAndroidSerial');
      if (result != null && result.toString().isNotEmpty) {
        return result.toString();
      }
    } catch (e) {
      debugPrint('⚠️ Platform channel غير متوفر: $e');
    }

    // طرق بديلة للحصول على معرف فريد
    try {
      // استخدام معلومات النظام المتاحة لإنشاء معرف فريد
      if (_deviceData != null) {
        final fingerprint = _deviceData!['fingerprint'];
        final androidId = _deviceData!['androidId'];

        if (fingerprint != null && fingerprint.toString().isNotEmpty) {
          // استخدام جزء من fingerprint كسيريال
          final fingerprintHash = fingerprint.toString().hashCode.abs();
          return 'FP_${fingerprintHash.toString().padLeft(10, '0')}';
        }

        if (androidId != null && androidId.toString().isNotEmpty) {
          return androidId.toString();
        }
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في الحصول على السيريال المحسن: $e');
    }

    return null;
  }

  /// الحصول على سيريال الويندوز المحسن
  static Future<String?> _getEnhancedWindowsSerial() async {
    try {
      // محاولة استخدام platform channel للحصول على السيريال الحقيقي
      final result = await _platformChannel.invokeMethod('getWindowsSerial');
      if (result != null && result.toString().isNotEmpty) {
        return result.toString();
      }
    } catch (e) {
      debugPrint('⚠️ Platform channel للويندوز غير متوفر: $e');
    }

    // استخدام معلومات النظام المتاحة
    try {
      if (_deviceData != null) {
        // ترتيب أولوية محسن للويندوز
        final candidates = [
          _deviceData!['digitalProductId'],
          _deviceData!['productId'],
          _deviceData!['buildLab'],
          _deviceData!['buildLabEx'],
        ];

        for (final candidate in candidates) {
          if (candidate != null && candidate.toString().isNotEmpty) {
            return candidate.toString();
          }
        }
      }
    } catch (e) {
      debugPrint('⚠️ خطأ في الحصول على سيريال الويندوز المحسن: $e');
    }

    return null;
  }

  /// إنشاء معرف فريد احتياطي من معلومات الجهاز
  static String _generateFallbackSerial() {
    if (_deviceData == null) return 'UNKNOWN_DEVICE';

    // جمع معلومات الجهاز المتاحة لإنشاء معرف فريد
    final platform = _deviceData!['platform'] ?? 'Unknown';
    final model = _deviceData!['model'] ?? _deviceData!['computerName'] ?? 'Unknown';
    final manufacturer = _deviceData!['manufacturer'] ?? _deviceData!['brand'] ?? 'Unknown';
    final fingerprint = _deviceData!['fingerprint'] ?? _deviceData!['systemGUID'] ?? '';

    // إنشاء معرف مركب
    final combinedInfo = '$platform-$model-$manufacturer-$fingerprint';

    // إنشاء hash بسيط من المعلومات المجمعة
    int hash = combinedInfo.hashCode;
    if (hash < 0) hash = -hash; // تأكد من أن الرقم موجب

    return '${platform.toUpperCase()}_${hash.toString().padLeft(10, '0')}';
  }
  
  /// الحصول على اسم الجهاز
  static String? get deviceName {
    if (_deviceData == null) return null;
    return _deviceData!['name'] ?? _deviceData!['model'] ?? _deviceData!['computerName'];
  }
  
  /// الحصول على نوع الجهاز/المنصة
  static String get devicePlatform {
    if (kIsWeb) return 'Web';
    if (Platform.isAndroid) return 'Android';
    if (Platform.isIOS) return 'iOS';
    if (Platform.isWindows) return 'Windows';
    if (Platform.isMacOS) return 'macOS';
    if (Platform.isLinux) return 'Linux';
    return 'Unknown';
  }
  
  /// جلب معلومات الجهاز من النظام
  static Future<Map<String, dynamic>> getDeviceInfo() async {
    try {
      debugPrint('📱 بدء جلب معلومات الجهاز...');
      
      Map<String, dynamic> deviceData = {};
      
      if (kIsWeb) {
        // معلومات المتصفح
        final webBrowserInfo = await _deviceInfo.webBrowserInfo;
        deviceData = {
          'platform': 'Web',
          'browserName': webBrowserInfo.browserName.name,
          'userAgent': webBrowserInfo.userAgent,
          'language': webBrowserInfo.language,
          'platform_details': webBrowserInfo.platform,
          'serial': 'WEB-${webBrowserInfo.userAgent?.hashCode ?? 'UNKNOWN'}',
        };
      } else if (Platform.isAndroid) {
        // معلومات Android مع تحسينات للحصول على السيريال
        final androidInfo = await _deviceInfo.androidInfo;

        // محاولة الحصول على السيريال بطرق متعددة
        String? serialNumber;
        String? enhancedSerial;

        try {
          serialNumber = androidInfo.serialNumber;
          // في الأندرويد 10+ قد يكون "unknown" أو فارغ
          if (serialNumber == "unknown" || serialNumber.isEmpty) {
            serialNumber = null;
          }
        } catch (e) {
          debugPrint('⚠️ لا يمكن الوصول لسيريال الأندرويد التقليدي: $e');
          serialNumber = null;
        }

        // محاولة الحصول على معلومات إضافية للسيريال
        try {
          enhancedSerial = await _getEnhancedAndroidSerial();
          debugPrint('🔍 Enhanced Android Serial: $enhancedSerial');
        } catch (e) {
          debugPrint('⚠️ لا يمكن الحصول على السيريال المحسن: $e');
        }

        // استخدام السيريال المحسن إذا كان متوفراً
        final finalSerial = enhancedSerial ?? serialNumber;

        deviceData = {
          'platform': 'Android',
          'model': androidInfo.model,
          'manufacturer': androidInfo.manufacturer,
          'brand': androidInfo.brand,
          'device': androidInfo.device,
          'product': androidInfo.product,
          'androidId': androidInfo.id, // هذا هو المعرف الأساسي للأندرويد
          'serial': finalSerial,
          'originalSerial': serialNumber, // الاحتفاظ بالسيريال الأصلي
          'enhancedSerial': enhancedSerial, // السيريال المحسن
          'version': androidInfo.version.release,
          'sdkInt': androidInfo.version.sdkInt,
          'fingerprint': androidInfo.fingerprint,
          'board': androidInfo.board,
          'bootloader': androidInfo.bootloader,
          'display': androidInfo.display,
          'hardware': androidInfo.hardware,
          'host': androidInfo.host,
          'tags': androidInfo.tags,
          'type': androidInfo.type,
          // معلومات إضافية مفيدة للتعرف على الجهاز
          'supportedAbis': androidInfo.supportedAbis,
          'supported32BitAbis': androidInfo.supported32BitAbis,
          'supported64BitAbis': androidInfo.supported64BitAbis,
        };
      } else if (Platform.isIOS) {
        // معلومات iOS
        final iosInfo = await _deviceInfo.iosInfo;
        deviceData = {
          'platform': 'iOS',
          'name': iosInfo.name,
          'model': iosInfo.model,
          'systemName': iosInfo.systemName,
          'systemVersion': iosInfo.systemVersion,
          'identifierForVendor': iosInfo.identifierForVendor,
          'serial': iosInfo.identifierForVendor, // iOS لا يوفر السيريال مباشرة
          'localizedModel': iosInfo.localizedModel,
          'utsname': iosInfo.utsname.machine,
        };
      } else if (Platform.isWindows) {
        // معلومات Windows مع تحسينات للحصول على السيريال
        final windowsInfo = await _deviceInfo.windowsInfo;

        // محاولة الحصول على السيريال المحسن
        String? enhancedSerial;
        try {
          enhancedSerial = await _getEnhancedWindowsSerial();
          debugPrint('🔍 Enhanced Windows Serial: $enhancedSerial');
        } catch (e) {
          debugPrint('⚠️ لا يمكن الحصول على السيريال المحسن للويندوز: $e');
        }

        // تحديد أفضل سيريال متاح
        String? finalSerial = enhancedSerial;
        if (finalSerial == null || finalSerial.isEmpty) {
          final candidates = [
            windowsInfo.deviceId.toString(),
            windowsInfo.productId.toString(),
            windowsInfo.digitalProductId.toString(),
            windowsInfo.buildLab.toString(),
          ];

          for (final candidate in candidates) {
            if (candidate.isNotEmpty && candidate != 'null') {
              finalSerial = candidate;
              break;
            }
          }
        }

        deviceData = {
          'platform': 'Windows',
          'computerName': windowsInfo.computerName,
          'numberOfCores': windowsInfo.numberOfCores,
          'systemMemoryInMegabytes': windowsInfo.systemMemoryInMegabytes,
          'userName': windowsInfo.userName,
          'majorVersion': windowsInfo.majorVersion,
          'minorVersion': windowsInfo.minorVersion,
          'buildNumber': windowsInfo.buildNumber,
          'platformId': windowsInfo.platformId,
          'csdVersion': windowsInfo.csdVersion,
          'servicePackMajor': windowsInfo.servicePackMajor,
          'servicePackMinor': windowsInfo.servicePackMinor,
          'suitMask': windowsInfo.suitMask,
          'productType': windowsInfo.productType,
          'reserved': windowsInfo.reserved,
          'buildLab': windowsInfo.buildLab,
          'buildLabEx': windowsInfo.buildLabEx,
          'digitalProductId': windowsInfo.digitalProductId,
          'displayVersion': windowsInfo.displayVersion,
          'editionId': windowsInfo.editionId,
          'installDate': windowsInfo.installDate,
          'productId': windowsInfo.productId,
          'productName': windowsInfo.productName,
          'registeredOwner': windowsInfo.registeredOwner,
          'releaseId': windowsInfo.releaseId,
          'deviceId': windowsInfo.deviceId,
          'serial': finalSerial, // استخدام أفضل سيريال متاح
          'enhancedSerial': enhancedSerial, // السيريال المحسن
          'originalDeviceId': windowsInfo.deviceId, // الاحتفاظ بالـ deviceId الأصلي
        };
      } else if (Platform.isMacOS) {
        // معلومات macOS
        final macOsInfo = await _deviceInfo.macOsInfo;
        deviceData = {
          'platform': 'macOS',
          'computerName': macOsInfo.computerName,
          'hostName': macOsInfo.hostName,
          'arch': macOsInfo.arch,
          'model': macOsInfo.model,
          'kernelVersion': macOsInfo.kernelVersion,
          'majorVersion': macOsInfo.majorVersion,
          'minorVersion': macOsInfo.minorVersion,
          'patchVersion': macOsInfo.patchVersion,
          'osRelease': macOsInfo.osRelease,
          'activeCPUs': macOsInfo.activeCPUs,
          'memorySize': macOsInfo.memorySize,
          'cpuFrequency': macOsInfo.cpuFrequency,
          'systemGUID': macOsInfo.systemGUID,
          'serial': macOsInfo.systemGUID, // استخدام systemGUID كسيريال
        };
      } else if (Platform.isLinux) {
        // معلومات Linux
        final linuxInfo = await _deviceInfo.linuxInfo;
        deviceData = {
          'platform': 'Linux',
          'name': linuxInfo.name,
          'version': linuxInfo.version,
          'id': linuxInfo.id,
          'idLike': linuxInfo.idLike,
          'versionCodename': linuxInfo.versionCodename,
          'versionId': linuxInfo.versionId,
          'prettyName': linuxInfo.prettyName,
          'buildId': linuxInfo.buildId,
          'variant': linuxInfo.variant,
          'variantId': linuxInfo.variantId,
          'machineId': linuxInfo.machineId,
          'serial': linuxInfo.machineId, // استخدام machineId كسيريال
        };
      }
      
      // حفظ البيانات محلياً
      _deviceData = deviceData;
      
      debugPrint('✅ تم جلب معلومات الجهاز بنجاح');
      debugPrint('📱 المنصة: ${deviceData['platform']}');
      debugPrint('🔢 السيريال: ${deviceSerial ?? 'غير متوفر'}');
      debugPrint('📝 اسم الجهاز: ${deviceName ?? 'غير متوفر'}');
      
      return {
        'success': true,
        'data': deviceData,
        'serial': deviceSerial,
        'name': deviceName,
        'platform': deviceData['platform'],
      };
      
    } catch (e) {
      debugPrint('❌ خطأ في جلب معلومات الجهاز: $e');
      debugPrint('🔍 تفاصيل الخطأ: ${e.runtimeType}');
      return {
        'success': false,
        'error': 'خطأ في جلب معلومات الجهاز: $e',
        'data': null,
        'serial': null,
        'name': null,
        'platform': devicePlatform,
      };
    }
  }
  
  /// طباعة معلومات الجهاز بشكل مفصل
  static void printDeviceInfo() {
    if (_deviceData == null) {
      debugPrint('⚠️ لم يتم جلب معلومات الجهاز بعد');
      return;
    }
    
    debugPrint('');
    debugPrint('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥');
    debugPrint('📱 معلومات الجهاز - Device Information');
    debugPrint('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥');
    debugPrint('🌐 المنصة (Platform): ${_deviceData!['platform']}');
    debugPrint('🔢 السيريال (Serial): ${deviceSerial ?? 'غير متوفر'}');
    debugPrint('📝 اسم الجهاز (Device Name): ${deviceName ?? 'غير متوفر'}');
    debugPrint('');
    debugPrint('📋 تفاصيل إضافية:');
    _deviceData!.forEach((key, value) {
      if (key != 'platform') {
        debugPrint('   $key: $value');
      }
    });
    debugPrint('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥');
    debugPrint('');
  }
  
  /// إعادة تعيين البيانات المحفوظة
  static void resetDeviceData() {
    _deviceData = null;
  }

  /// الحصول على معلومات السيريال مع التفاصيل
  static Map<String, dynamic> getSerialInfo() {
    if (_deviceData == null) {
      return {
        'serial': null,
        'source': 'not_loaded',
        'platform': 'unknown',
        'available_ids': [],
        'error': 'Device data not loaded',
      };
    }

    final platform = _deviceData!['platform'];
    List<String> availableIds = [];
    String? primarySerial;
    String source = 'unknown';

    if (platform == 'Android') {
      // عرض المعرفات المتاحة للأندرويد مع التحسينات
      if (_deviceData!['enhancedSerial'] != null && _deviceData!['enhancedSerial'].toString().isNotEmpty) {
        availableIds.add('enhancedSerial: ${_deviceData!['enhancedSerial']}');
        primarySerial ??= _deviceData!['enhancedSerial'];
        source = 'enhanced_serial';
      }
      if (_deviceData!['serial'] != null && _deviceData!['serial'].toString().isNotEmpty && _deviceData!['serial'].toString().toLowerCase() != 'unknown') {
        availableIds.add('serial: ${_deviceData!['serial']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['serial'];
          source = 'hardware_serial';
        }
      }
      if (_deviceData!['androidId'] != null && _deviceData!['androidId'].toString().isNotEmpty) {
        availableIds.add('androidId: ${_deviceData!['androidId']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['androidId'];
          source = 'android_id';
        }
      }
      if (_deviceData!['fingerprint'] != null && _deviceData!['fingerprint'].toString().isNotEmpty) {
        availableIds.add('fingerprint: ${_deviceData!['fingerprint']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['fingerprint'];
          source = 'build_fingerprint';
        }
      }
    } else if (platform == 'iOS') {
      if (_deviceData!['identifierForVendor'] != null) {
        availableIds.add('identifierForVendor: ${_deviceData!['identifierForVendor']}');
        primarySerial = _deviceData!['identifierForVendor'];
        source = 'identifierForVendor';
      }
    } else if (platform == 'Windows') {
      // عرض المعرفات المتاحة للويندوز مع التحسينات
      if (_deviceData!['enhancedSerial'] != null && _deviceData!['enhancedSerial'].toString().isNotEmpty) {
        availableIds.add('enhancedSerial: ${_deviceData!['enhancedSerial']}');
        primarySerial ??= _deviceData!['enhancedSerial'];
        source = 'enhanced_serial';
      }
      if (_deviceData!['deviceId'] != null && _deviceData!['deviceId'].toString().isNotEmpty) {
        availableIds.add('deviceId: ${_deviceData!['deviceId']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['deviceId'];
          source = 'device_id';
        }
      }
      if (_deviceData!['productId'] != null && _deviceData!['productId'].toString().isNotEmpty) {
        availableIds.add('productId: ${_deviceData!['productId']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['productId'];
          source = 'product_id';
        }
      }
      if (_deviceData!['digitalProductId'] != null && _deviceData!['digitalProductId'].toString().isNotEmpty) {
        availableIds.add('digitalProductId: ${_deviceData!['digitalProductId']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['digitalProductId'];
          source = 'digital_product_id';
        }
      }
      if (_deviceData!['computerName'] != null && _deviceData!['computerName'].toString().isNotEmpty) {
        availableIds.add('computerName: ${_deviceData!['computerName']}');
        if (primarySerial == null) {
          primarySerial = _deviceData!['computerName'];
          source = 'computer_name';
        }
      }
    }

    // إذا لم نجد معرف، استخدم المعرف الاحتياطي
    if (primarySerial == null || primarySerial.toString().isEmpty) {
      primarySerial = _generateFallbackSerial();
      source = 'generated_fallback';
    }

    return {
      'serial': primarySerial,
      'source': source,
      'platform': platform,
      'available_ids': availableIds,
      'error': null,
    };
  }

  /// طباعة معلومات السيريال للتشخيص المحسن
  static void printSerialDiagnostics() {
    debugPrint('');
    debugPrint('🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍');
    debugPrint('🔑 تشخيص سيريال الجهاز المحسن - Enhanced Serial Diagnostics');
    debugPrint('🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍');

    final serialInfo = getSerialInfo();

    debugPrint('📱 المنصة: ${serialInfo['platform']}');
    debugPrint('🔑 السيريال المستخدم: ${serialInfo['serial'] ?? "غير متوفر"}');
    debugPrint('📍 مصدر السيريال: ${serialInfo['source']}');

    // معلومات إضافية للأندرويد
    if (serialInfo['platform'] == 'Android' && _deviceData != null) {
      debugPrint('');
      debugPrint('🤖 تفاصيل الأندرويد:');
      if (_deviceData!['enhancedSerial'] != null) {
        debugPrint('   🔧 السيريال المحسن: ${_deviceData!['enhancedSerial']}');
      }
      if (_deviceData!['originalSerial'] != null) {
        debugPrint('   📱 السيريال الأصلي: ${_deviceData!['originalSerial']}');
      }
      debugPrint('   🆔 Android ID: ${_deviceData!['androidId'] ?? "غير متوفر"}');
      debugPrint('   📋 SDK Version: ${_deviceData!['sdkInt'] ?? "غير متوفر"}');
    }

    // معلومات إضافية للويندوز
    if (serialInfo['platform'] == 'Windows' && _deviceData != null) {
      debugPrint('');
      debugPrint('🪟 تفاصيل الويندوز:');
      if (_deviceData!['enhancedSerial'] != null) {
        debugPrint('   🔧 السيريال المحسن: ${_deviceData!['enhancedSerial']}');
      }
      if (_deviceData!['originalDeviceId'] != null) {
        debugPrint('   🆔 Device ID الأصلي: ${_deviceData!['originalDeviceId']}');
      }
      debugPrint('   💻 اسم الكمبيوتر: ${_deviceData!['computerName'] ?? "غير متوفر"}');
      debugPrint('   🏷️ Product ID: ${_deviceData!['productId'] ?? "غير متوفر"}');
    }

    if (serialInfo['available_ids'].isNotEmpty) {
      debugPrint('');
      debugPrint('📋 جميع المعرفات المتاحة:');
      for (String id in serialInfo['available_ids']) {
        debugPrint('   - $id');
      }
    } else {
      debugPrint('⚠️ لا توجد معرفات متاحة');
    }

    if (serialInfo['error'] != null) {
      debugPrint('❌ خطأ: ${serialInfo['error']}');
    }

    debugPrint('🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍🔍');
    debugPrint('');
  }
}
