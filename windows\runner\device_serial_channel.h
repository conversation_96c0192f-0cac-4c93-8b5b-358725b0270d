#ifndef DEVICE_SERIAL_CHANNEL_H_
#define DEVICE_SERIAL_CHANNEL_H_

#include <flutter/method_channel.h>
#include <flutter/plugin_registrar_windows.h>
#include <flutter/standard_method_codec.h>
#include <memory>
#include <string>

/**
 * قناة مخصصة للحصول على سيريال الجهاز في الويندوز
 * Enhanced Device Serial Channel for Windows serial number retrieval
 */
class DeviceSerialChannel {
 public:
  static void RegisterWithRegistrar(flutter::PluginRegistrarWindows* registrar);

 private:
  DeviceSerialChannel();
  ~DeviceSerialChannel();

  // منع النسخ والتعيين
  DeviceSerialChannel(const DeviceSerialChannel&) = delete;
  DeviceSerialChannel& operator=(const DeviceSerialChannel&) = delete;

  // معالج استدعاءات الطرق
  void HandleMethodCall(
      const flutter::MethodCall<flutter::EncodableValue>& method_call,
      std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result);

  // طرق الحصول على السيريال
  std::string GetEnhancedWindowsSerial();
  std::string GetWindowsSerialFromWMI();
  std::string GetWindowsSerialFromRegistry();
  std::string GetWindowsSerialFromSystemInfo();
  std::string GetMachineGuid();
  std::string GetBiosSerial();
  std::string GetMotherboardSerial();
  std::string GetProcessorId();
  std::string CreateFallbackSerial();

  // مساعدات
  std::string ExecuteCommand(const std::string& command);
  std::string GetRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName);
  std::string GenerateHashFromSystemInfo();

  std::unique_ptr<flutter::MethodChannel<flutter::EncodableValue>> channel_;
};

#endif  // DEVICE_SERIAL_CHANNEL_H_
