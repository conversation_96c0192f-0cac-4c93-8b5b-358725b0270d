import 'package:flutter/material.dart';
import 'lib/services/device_info_service.dart';

/// ملف اختبار لتجربة تحسينات الحصول على سيريال الجهاز
/// Test file for device serial improvements
void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  
  print('🚀 بدء اختبار تحسينات سيريال الجهاز...');
  print('🚀 Starting device serial improvements test...');
  print('=' * 60);
  
  await testDeviceSerial();
}

Future<void> testDeviceSerial() async {
  try {
    print('📱 جلب معلومات الجهاز...');
    print('📱 Fetching device information...');
    
    // جلب معلومات الجهاز
    final result = await DeviceInfoService.getDeviceInfo();
    
    if (result['success'] == true) {
      print('✅ تم جلب معلومات الجهاز بنجاح');
      print('✅ Device information fetched successfully');
      print('-' * 40);
      
      // طباعة المنصة
      print('🌐 المنصة: ${DeviceInfoService.devicePlatform}');
      print('🌐 Platform: ${DeviceInfoService.devicePlatform}');
      
      // طباعة السيريال الأساسي
      String? serial = DeviceInfoService.deviceSerial;
      print('🔑 السيريال: ${serial ?? "غير متوفر"}');
      print('🔑 Serial: ${serial ?? "Not available"}');
      
      // طباعة اسم الجهاز
      String? deviceName = DeviceInfoService.deviceName;
      print('📱 اسم الجهاز: ${deviceName ?? "غير متوفر"}');
      print('📱 Device Name: ${deviceName ?? "Not available"}');
      
      print('-' * 40);
      
      // طباعة تشخيص مفصل
      print('🔍 تشخيص مفصل للسيريال:');
      print('🔍 Detailed serial diagnostics:');
      DeviceInfoService.printSerialDiagnostics();
      
      print('-' * 40);
      
      // طباعة معلومات السيريال
      final serialInfo = DeviceInfoService.getSerialInfo();
      print('📋 معلومات السيريال:');
      print('📋 Serial Information:');
      print('   - السيريال المستخدم: ${serialInfo['serial']}');
      print('   - Used Serial: ${serialInfo['serial']}');
      print('   - المصدر: ${serialInfo['source']}');
      print('   - Source: ${serialInfo['source']}');
      print('   - المنصة: ${serialInfo['platform']}');
      print('   - Platform: ${serialInfo['platform']}');
      
      if (serialInfo['available_ids'].isNotEmpty) {
        print('   - المعرفات المتاحة:');
        print('   - Available IDs:');
        for (String id in serialInfo['available_ids']) {
          print('     * $id');
        }
      }
      
      if (serialInfo['error'] != null) {
        print('   - خطأ: ${serialInfo['error']}');
        print('   - Error: ${serialInfo['error']}');
      }
      
      print('-' * 40);
      
      // اختبار معلومات إضافية حسب المنصة
      await testPlatformSpecificInfo();
      
    } else {
      print('❌ فشل جلب معلومات الجهاز: ${result['error']}');
      print('❌ Failed to fetch device information: ${result['error']}');
    }
    
  } catch (e) {
    print('💥 خطأ غير متوقع: $e');
    print('💥 Unexpected error: $e');
  }
  
  print('=' * 60);
  print('🏁 انتهى الاختبار');
  print('🏁 Test completed');
}

Future<void> testPlatformSpecificInfo() async {
  final deviceData = DeviceInfoService.deviceData;
  if (deviceData == null) return;
  
  final platform = deviceData['platform'];
  
  print('🔧 معلومات خاصة بالمنصة ($platform):');
  print('🔧 Platform-specific information ($platform):');
  
  if (platform == 'Android') {
    print('🤖 معلومات الأندرويد:');
    print('🤖 Android Information:');
    
    if (deviceData['enhancedSerial'] != null) {
      print('   - السيريال المحسن: ${deviceData['enhancedSerial']}');
      print('   - Enhanced Serial: ${deviceData['enhancedSerial']}');
    }
    
    if (deviceData['originalSerial'] != null) {
      print('   - السيريال الأصلي: ${deviceData['originalSerial']}');
      print('   - Original Serial: ${deviceData['originalSerial']}');
    }
    
    print('   - Android ID: ${deviceData['androidId'] ?? "غير متوفر"}');
    print('   - Android ID: ${deviceData['androidId'] ?? "Not available"}');
    
    print('   - إصدار SDK: ${deviceData['sdkInt'] ?? "غير متوفر"}');
    print('   - SDK Version: ${deviceData['sdkInt'] ?? "Not available"}');
    
    print('   - الشركة المصنعة: ${deviceData['manufacturer'] ?? "غير متوفر"}');
    print('   - Manufacturer: ${deviceData['manufacturer'] ?? "Not available"}');
    
    print('   - الموديل: ${deviceData['model'] ?? "غير متوفر"}');
    print('   - Model: ${deviceData['model'] ?? "Not available"}');
    
  } else if (platform == 'Windows') {
    print('🪟 معلومات الويندوز:');
    print('🪟 Windows Information:');
    
    if (deviceData['enhancedSerial'] != null) {
      print('   - السيريال المحسن: ${deviceData['enhancedSerial']}');
      print('   - Enhanced Serial: ${deviceData['enhancedSerial']}');
    }
    
    if (deviceData['originalDeviceId'] != null) {
      print('   - Device ID الأصلي: ${deviceData['originalDeviceId']}');
      print('   - Original Device ID: ${deviceData['originalDeviceId']}');
    }
    
    print('   - اسم الكمبيوتر: ${deviceData['computerName'] ?? "غير متوفر"}');
    print('   - Computer Name: ${deviceData['computerName'] ?? "Not available"}');
    
    print('   - Product ID: ${deviceData['productId'] ?? "غير متوفر"}');
    print('   - Product ID: ${deviceData['productId'] ?? "Not available"}');
    
    print('   - إصدار الويندوز: ${deviceData['majorVersion']}.${deviceData['minorVersion']}');
    print('   - Windows Version: ${deviceData['majorVersion']}.${deviceData['minorVersion']}');
    
    print('   - Build Number: ${deviceData['buildNumber'] ?? "غير متوفر"}');
    print('   - Build Number: ${deviceData['buildNumber'] ?? "Not available"}');
    
  } else if (platform == 'iOS') {
    print('🍎 معلومات iOS:');
    print('🍎 iOS Information:');
    
    print('   - Identifier For Vendor: ${deviceData['identifierForVendor'] ?? "غير متوفر"}');
    print('   - Identifier For Vendor: ${deviceData['identifierForVendor'] ?? "Not available"}');
    
    print('   - اسم الجهاز: ${deviceData['name'] ?? "غير متوفر"}');
    print('   - Device Name: ${deviceData['name'] ?? "Not available"}');
    
    print('   - الموديل: ${deviceData['model'] ?? "غير متوفر"}');
    print('   - Model: ${deviceData['model'] ?? "Not available"}');
    
    print('   - إصدار النظام: ${deviceData['systemVersion'] ?? "غير متوفر"}');
    print('   - System Version: ${deviceData['systemVersion'] ?? "Not available"}');
  }
}
