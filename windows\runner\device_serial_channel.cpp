#include "device_serial_channel.h"

#include <windows.h>
#include <comdef.h>
#include <wbemidl.h>
#include <iostream>
#include <sstream>
#include <iomanip>
#include <memory>
#include <vector>

#pragma comment(lib, "wbemuuid.lib")

// تسجيل القناة مع المسجل
void DeviceSerialChannel::RegisterWithRegistrar(flutter::PluginRegistrarWindows* registrar) {
  auto channel = std::make_unique<flutter::MethodChannel<flutter::EncodableValue>>(
      registrar->messenger(), "device_serial_channel",
      &flutter::StandardMethodCodec::GetInstance());

  auto plugin = std::make_unique<DeviceSerialChannel>();

  channel->SetMethodCallHandler(
      [plugin_pointer = plugin.get()](const auto& call, auto result) {
        plugin_pointer->HandleMethodCall(call, std::move(result));
      });

  registrar->AddPlugin(std::move(plugin));
}

DeviceSerialChannel::DeviceSerialChannel() {}

DeviceSerialChannel::~DeviceSerialChannel() {}

// معالج استدعاءات الطرق
void DeviceSerialChannel::HandleMethodCall(
    const flutter::MethodCall<flutter::EncodableValue>& method_call,
    std::unique_ptr<flutter::MethodResult<flutter::EncodableValue>> result) {
  
  if (method_call.method_name().compare("getWindowsSerial") == 0) {
    try {
      std::string serial = GetEnhancedWindowsSerial();
      result->Success(flutter::EncodableValue(serial));
    } catch (const std::exception& e) {
      result->Error("SERIAL_ERROR", "Failed to get Windows serial: " + std::string(e.what()));
    }
  } else {
    result->NotImplemented();
  }
}

// الحصول على سيريال الويندوز المحسن
std::string DeviceSerialChannel::GetEnhancedWindowsSerial() {
  std::string serial;

  // الطريقة الأولى: محاولة الحصول على السيريال من WMI
  try {
    serial = GetWindowsSerialFromWMI();
    if (!serial.empty() && serial != "To be filled by O.E.M." && serial != "Default string") {
      return "WMI_" + serial;
    }
  } catch (...) {
    std::cout << "WMI method failed" << std::endl;
  }

  // الطريقة الثانية: محاولة الحصول على السيريال من Registry
  try {
    serial = GetWindowsSerialFromRegistry();
    if (!serial.empty()) {
      return "REG_" + serial;
    }
  } catch (...) {
    std::cout << "Registry method failed" << std::endl;
  }

  // الطريقة الثالثة: الحصول على Machine GUID
  try {
    serial = GetMachineGuid();
    if (!serial.empty()) {
      return "GUID_" + serial;
    }
  } catch (...) {
    std::cout << "Machine GUID method failed" << std::endl;
  }

  // الطريقة الرابعة: الحصول على BIOS Serial
  try {
    serial = GetBiosSerial();
    if (!serial.empty() && serial != "To be filled by O.E.M.") {
      return "BIOS_" + serial;
    }
  } catch (...) {
    std::cout << "BIOS serial method failed" << std::endl;
  }

  // الطريقة الخامسة: الحصول على Motherboard Serial
  try {
    serial = GetMotherboardSerial();
    if (!serial.empty() && serial != "To be filled by O.E.M.") {
      return "MB_" + serial;
    }
  } catch (...) {
    std::cout << "Motherboard serial method failed" << std::endl;
  }

  // الطريقة السادسة: الحصول على Processor ID
  try {
    serial = GetProcessorId();
    if (!serial.empty()) {
      return "CPU_" + serial;
    }
  } catch (...) {
    std::cout << "Processor ID method failed" << std::endl;
  }

  // إذا فشلت جميع الطرق، أنشئ معرف احتياطي
  return CreateFallbackSerial();
}

// الحصول على السيريال من WMI
std::string DeviceSerialChannel::GetWindowsSerialFromWMI() {
  HRESULT hres;
  
  // تهيئة COM
  hres = CoInitializeEx(0, COINIT_MULTITHREADED);
  if (FAILED(hres)) {
    return "";
  }

  // تهيئة COM security
  hres = CoInitializeSecurity(
    NULL, -1, NULL, NULL,
    RPC_C_AUTHN_LEVEL_NONE,
    RPC_C_IMP_LEVEL_IMPERSONATE,
    NULL, EOAC_NONE, NULL);

  if (FAILED(hres)) {
    CoUninitialize();
    return "";
  }

  IWbemLocator *pLoc = NULL;
  hres = CoCreateInstance(
    CLSID_WbemLocator, 0,
    CLSCTX_INPROC_SERVER,
    IID_IWbemLocator, (LPVOID *) &pLoc);

  if (FAILED(hres)) {
    CoUninitialize();
    return "";
  }

  IWbemServices *pSvc = NULL;
  hres = pLoc->ConnectServer(
    _bstr_t(L"ROOT\\CIMV2"),
    NULL, NULL, 0, NULL, 0, 0, &pSvc);

  if (FAILED(hres)) {
    pLoc->Release();
    CoUninitialize();
    return "";
  }

  hres = CoSetProxyBlanket(
    pSvc, RPC_C_AUTHN_WINNT, RPC_C_AUTHZ_NONE, NULL,
    RPC_C_AUTHN_LEVEL_CALL, RPC_C_IMP_LEVEL_IMPERSONATE,
    NULL, EOAC_NONE);

  if (FAILED(hres)) {
    pSvc->Release();
    pLoc->Release();
    CoUninitialize();
    return "";
  }

  IEnumWbemClassObject* pEnumerator = NULL;
  hres = pSvc->ExecQuery(
    bstr_t("WQL"),
    bstr_t("SELECT SerialNumber FROM Win32_BaseBoard"),
    WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY,
    NULL, &pEnumerator);

  if (FAILED(hres)) {
    pSvc->Release();
    pLoc->Release();
    CoUninitialize();
    return "";
  }

  IWbemClassObject *pclsObj = NULL;
  ULONG uReturn = 0;
  std::string result = "";

  while (pEnumerator) {
    HRESULT hr = pEnumerator->Next(WBEM_INFINITE, 1, &pclsObj, &uReturn);
    if (0 == uReturn) break;

    VARIANT vtProp;
    hr = pclsObj->Get(L"SerialNumber", 0, &vtProp, 0, 0);
    if (SUCCEEDED(hr) && vtProp.vt == VT_BSTR) {
      _bstr_t bstrSerial(vtProp.bstrVal);
      result = std::string(bstrSerial);
      VariantClear(&vtProp);
      break;
    }
    VariantClear(&vtProp);
    pclsObj->Release();
  }

  pSvc->Release();
  pLoc->Release();
  pEnumerator->Release();
  CoUninitialize();

  return result;
}

// الحصول على السيريال من Registry
std::string DeviceSerialChannel::GetWindowsSerialFromRegistry() {
  return GetRegistryValue(HKEY_LOCAL_MACHINE, 
    "SOFTWARE\\Microsoft\\Windows NT\\CurrentVersion", 
    "ProductId");
}

// الحصول على Machine GUID
std::string DeviceSerialChannel::GetMachineGuid() {
  return GetRegistryValue(HKEY_LOCAL_MACHINE,
    "SOFTWARE\\Microsoft\\Cryptography",
    "MachineGuid");
}

// الحصول على BIOS Serial
std::string DeviceSerialChannel::GetBiosSerial() {
  // استخدام WMI للحصول على BIOS Serial
  HRESULT hres = CoInitializeEx(0, COINIT_MULTITHREADED);
  if (FAILED(hres)) return "";

  IWbemLocator *pLoc = NULL;
  hres = CoCreateInstance(CLSID_WbemLocator, 0, CLSCTX_INPROC_SERVER, IID_IWbemLocator, (LPVOID *) &pLoc);
  if (FAILED(hres)) {
    CoUninitialize();
    return "";
  }

  IWbemServices *pSvc = NULL;
  hres = pLoc->ConnectServer(_bstr_t(L"ROOT\\CIMV2"), NULL, NULL, 0, NULL, 0, 0, &pSvc);
  if (FAILED(hres)) {
    pLoc->Release();
    CoUninitialize();
    return "";
  }

  IEnumWbemClassObject* pEnumerator = NULL;
  hres = pSvc->ExecQuery(bstr_t("WQL"), bstr_t("SELECT SerialNumber FROM Win32_BIOS"),
    WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY, NULL, &pEnumerator);

  std::string result = "";
  if (SUCCEEDED(hres)) {
    IWbemClassObject *pclsObj = NULL;
    ULONG uReturn = 0;

    while (pEnumerator) {
      HRESULT hr = pEnumerator->Next(WBEM_INFINITE, 1, &pclsObj, &uReturn);
      if (0 == uReturn) break;

      VARIANT vtProp;
      hr = pclsObj->Get(L"SerialNumber", 0, &vtProp, 0, 0);
      if (SUCCEEDED(hr) && vtProp.vt == VT_BSTR) {
        _bstr_t bstrSerial(vtProp.bstrVal);
        result = std::string(bstrSerial);
        VariantClear(&vtProp);
        break;
      }
      VariantClear(&vtProp);
      pclsObj->Release();
    }
    pEnumerator->Release();
  }

  pSvc->Release();
  pLoc->Release();
  CoUninitialize();
  return result;
}

// الحصول على Motherboard Serial
std::string DeviceSerialChannel::GetMotherboardSerial() {
  // نفس الطريقة المستخدمة في GetWindowsSerialFromWMI
  return GetWindowsSerialFromWMI();
}

// الحصول على Processor ID
std::string DeviceSerialChannel::GetProcessorId() {
  HRESULT hres = CoInitializeEx(0, COINIT_MULTITHREADED);
  if (FAILED(hres)) return "";

  IWbemLocator *pLoc = NULL;
  hres = CoCreateInstance(CLSID_WbemLocator, 0, CLSCTX_INPROC_SERVER, IID_IWbemLocator, (LPVOID *) &pLoc);
  if (FAILED(hres)) {
    CoUninitialize();
    return "";
  }

  IWbemServices *pSvc = NULL;
  hres = pLoc->ConnectServer(_bstr_t(L"ROOT\\CIMV2"), NULL, NULL, 0, NULL, 0, 0, &pSvc);
  if (FAILED(hres)) {
    pLoc->Release();
    CoUninitialize();
    return "";
  }

  IEnumWbemClassObject* pEnumerator = NULL;
  hres = pSvc->ExecQuery(bstr_t("WQL"), bstr_t("SELECT ProcessorId FROM Win32_Processor"),
    WBEM_FLAG_FORWARD_ONLY | WBEM_FLAG_RETURN_IMMEDIATELY, NULL, &pEnumerator);

  std::string result = "";
  if (SUCCEEDED(hres)) {
    IWbemClassObject *pclsObj = NULL;
    ULONG uReturn = 0;

    while (pEnumerator) {
      HRESULT hr = pEnumerator->Next(WBEM_INFINITE, 1, &pclsObj, &uReturn);
      if (0 == uReturn) break;

      VARIANT vtProp;
      hr = pclsObj->Get(L"ProcessorId", 0, &vtProp, 0, 0);
      if (SUCCEEDED(hr) && vtProp.vt == VT_BSTR) {
        _bstr_t bstrSerial(vtProp.bstrVal);
        result = std::string(bstrSerial);
        VariantClear(&vtProp);
        break;
      }
      VariantClear(&vtProp);
      pclsObj->Release();
    }
    pEnumerator->Release();
  }

  pSvc->Release();
  pLoc->Release();
  CoUninitialize();
  return result;
}

// إنشاء معرف احتياطي
std::string DeviceSerialChannel::CreateFallbackSerial() {
  return GenerateHashFromSystemInfo();
}

// الحصول على قيمة من Registry
std::string DeviceSerialChannel::GetRegistryValue(HKEY hKey, const std::string& subKey, const std::string& valueName) {
  HKEY hSubKey;
  LONG result = RegOpenKeyExA(hKey, subKey.c_str(), 0, KEY_READ, &hSubKey);
  if (result != ERROR_SUCCESS) {
    return "";
  }

  DWORD dataSize = 0;
  result = RegQueryValueExA(hSubKey, valueName.c_str(), NULL, NULL, NULL, &dataSize);
  if (result != ERROR_SUCCESS) {
    RegCloseKey(hSubKey);
    return "";
  }

  std::vector<char> data(dataSize);
  result = RegQueryValueExA(hSubKey, valueName.c_str(), NULL, NULL, (LPBYTE)data.data(), &dataSize);
  RegCloseKey(hSubKey);

  if (result == ERROR_SUCCESS) {
    return std::string(data.data());
  }
  return "";
}

// إنشاء hash من معلومات النظام
std::string DeviceSerialChannel::GenerateHashFromSystemInfo() {
  std::stringstream ss;

  // جمع معلومات النظام
  char computerName[MAX_COMPUTERNAME_LENGTH + 1];
  DWORD size = sizeof(computerName);
  if (GetComputerNameA(computerName, &size)) {
    ss << computerName << "_";
  }

  // إضافة معلومات إضافية
  SYSTEM_INFO sysInfo;
  GetSystemInfo(&sysInfo);
  ss << sysInfo.dwProcessorType << "_" << sysInfo.dwNumberOfProcessors << "_";

  // إنشاء hash بسيط
  std::string combined = ss.str();
  std::hash<std::string> hasher;
  size_t hash = hasher(combined);

  std::stringstream hashStream;
  hashStream << "FALLBACK_" << std::hex << hash;
  return hashStream.str();
}
