package com.example.uptime_smart_assist

import android.content.Context
import android.os.Build
import android.provider.Settings
import android.telephony.TelephonyManager
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import java.lang.reflect.Method
import kotlin.math.abs

/**
 * قناة مخصصة للحصول على سيريال الجهاز بطرق محسنة
 * Enhanced Device Serial Channel for better serial number retrieval
 */
class DeviceSerialChannel(private val context: Context) : MethodChannel.MethodCallHandler {

    override fun onMethodCall(call: MethodCall, result: MethodChannel.Result) {
        when (call.method) {
            "getAndroidSerial" -> {
                try {
                    val serial = getEnhancedAndroidSerial()
                    result.success(serial)
                } catch (e: Exception) {
                    result.error("SERIAL_ERROR", "Failed to get Android serial: ${e.message}", null)
                }
            }
            else -> result.notImplemented()
        }
    }

    /**
     * الحصول على سيريال الأندرويد بطرق محسنة
     * Get Android serial using enhanced methods
     */
    private fun getEnhancedAndroidSerial(): String {
        var serial: String? = null

        // الطريقة الأولى: محاولة الحصول على السيريال من Build
        try {
            serial = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                // Android 8.0+ يتطلب صلاحية READ_PHONE_STATE
                Build.getSerial()
            } else {
                // Android 7.1 وأقل
                Build.SERIAL
            }
            
            if (!serial.isNullOrEmpty() && serial != "unknown") {
                return "BUILD_$serial"
            }
        } catch (e: SecurityException) {
            // لا توجد صلاحية READ_PHONE_STATE
            println("SecurityException: ${e.message}")
        } catch (e: Exception) {
            println("Exception getting Build serial: ${e.message}")
        }

        // الطريقة الثانية: استخدام Android ID
        try {
            val androidId = Settings.Secure.getString(context.contentResolver, Settings.Secure.ANDROID_ID)
            if (!androidId.isNullOrEmpty() && androidId != "9774d56d682e549c") {
                // تجنب Android ID المعروف للأجهزة المقلدة
                return "AID_$androidId"
            }
        } catch (e: Exception) {
            println("Exception getting Android ID: ${e.message}")
        }

        // الطريقة الثالثة: استخدام معلومات الجهاز المركبة
        try {
            val deviceInfo = "${Build.MANUFACTURER}_${Build.MODEL}_${Build.DEVICE}_${Build.BOARD}_${Build.HARDWARE}"
            
            // إنشاء hash من معلومات الجهاز
            val hash = abs(deviceInfo.hashCode())
            return "DEVICE_${String.format("%010d", hash)}"
        } catch (e: Exception) {
            println("Exception creating device info serial: ${e.message}")
        }

        // الطريقة الرابعة: استخدام Build.FINGERPRINT
        try {
            if (!Build.FINGERPRINT.isNullOrEmpty()) {
                val hash = abs(Build.FINGERPRINT.hashCode())
                return "FP_${String.format("%010d", hash)}"
            }
        } catch (e: Exception) {
            println("Exception using fingerprint: ${e.message}")
        }

        // الطريقة الخامسة: استخدام Reflection للوصول لطرق مخفية
        try {
            val reflectionSerial = getSerialUsingReflection()
            if (!reflectionSerial.isNullOrEmpty()) {
                return reflectionSerial
            }
        } catch (e: Exception) {
            println("Reflection method failed: ${e.message}")
        }

        // إذا فشلت جميع الطرق، أرجع معرف افتراضي
        return "FALLBACK_${System.currentTimeMillis()}"
    }

    /**
     * محاولة الحصول على IMEI (يتطلب صلاحيات خاصة)
     * Attempt to get IMEI (requires special permissions)
     */
    private fun getIMEI(): String? {
        return try {
            val telephonyManager = context.getSystemService(Context.TELEPHONY_SERVICE) as TelephonyManager?
            telephonyManager?.let {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                    it.imei
                } else {
                    @Suppress("DEPRECATION")
                    it.deviceId
                }
            }
        } catch (e: SecurityException) {
            // لا توجد صلاحية READ_PHONE_STATE
            println("SecurityException getting IMEI: ${e.message}")
            null
        } catch (e: Exception) {
            println("Exception getting IMEI: ${e.message}")
            null
        }
    }

    /**
     * استخدام Reflection للوصول لطرق مخفية (قد لا تعمل في جميع الأجهزة)
     * Use reflection to access hidden methods (may not work on all devices)
     */
    private fun getSerialUsingReflection(): String? {
        return try {
            val c = Class.forName("android.os.SystemProperties")
            val get = c.getMethod("get", String::class.java)
            val serial = get.invoke(c, "ro.serialno") as String?
            if (!serial.isNullOrEmpty() && serial != "unknown") {
                "PROP_$serial"
            } else {
                null
            }
        } catch (e: Exception) {
            println("Reflection method failed: ${e.message}")
            null
        }
    }

    /**
     * الحصول على معلومات إضافية عن الجهاز
     * Get additional device information
     */
    private fun getDeviceFingerprint(): String {
        return try {
            val info = StringBuilder()
            info.append("BRAND:${Build.BRAND}_")
            info.append("MODEL:${Build.MODEL}_")
            info.append("MANUFACTURER:${Build.MANUFACTURER}_")
            info.append("DEVICE:${Build.DEVICE}_")
            info.append("PRODUCT:${Build.PRODUCT}_")
            info.append("BOARD:${Build.BOARD}_")
            info.append("HARDWARE:${Build.HARDWARE}_")
            info.append("SDK:${Build.VERSION.SDK_INT}_")
            info.append("RELEASE:${Build.VERSION.RELEASE}")
            
            val hash = abs(info.toString().hashCode())
            "FINGERPRINT_${String.format("%010d", hash)}"
        } catch (e: Exception) {
            println("Exception creating device fingerprint: ${e.message}")
            "FINGERPRINT_ERROR"
        }
    }
}
